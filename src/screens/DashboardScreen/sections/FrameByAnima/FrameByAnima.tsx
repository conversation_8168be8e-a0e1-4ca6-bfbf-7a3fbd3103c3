import {
  BarChartIcon,
  LayoutDashboardIcon,
  PercentIcon,
  SettingsIcon,
} from "lucide-react";
import React from "react";

export const FrameByAnima = (): JSX.Element => {
  // Navigation items data for mapping
  const navItems = [
    {
      id: 1,
      label: "Dashboard",
      icon: <LayoutDashboardIcon size={18} />,
      active: true,
    },
    {
      id: 2,
      label: "Discount Rules",
      icon: <PercentIcon size={18} />,
      active: false,
    },
    {
      id: 3,
      label: "Analytics",
      icon: <BarChartIcon size={18} />,
      active: false,
    },
    {
      id: 4,
      label: "Settings",
      icon: <SettingsIcon size={18} />,
      active: false,
    },
  ];

  return (
    <header className="w-full h-[84px] bg-[#fcfcfc] rounded-xl flex items-center justify-between px-[140px]">
      <div className="font-button-text-md font-[number:var(--button-text-md-font-weight)] text-gray-800 text-[length:var(--button-text-md-font-size)] tracking-[var(--button-text-md-letter-spacing)] leading-[var(--button-text-md-line-height)] whitespace-nowrap [font-style:var(--button-text-md-font-style)]">
        ADM Logo
      </div>

      <nav className="flex items-center p-[5px] bg-[#f4f6f9] rounded-[35px]">
        <div className="flex items-center gap-2">
          {navItems.map((item) => (
            <div
              key={item.id}
              className={`h-[42px] flex items-center gap-2 px-3 py-[10px] rounded-[28px] ${
                item.active ? "bg-[#ff6f44]" : ""
              }`}
            >
              <span className={item.active ? "text-white" : "text-gray-500"}>
                {item.icon}
              </span>
              <span
                className={`[font-family:'Manrope',Helvetica] font-medium text-sm tracking-[0] leading-[21px] whitespace-nowrap ${
                  item.active ? "text-white" : "text-gray-500"
                }`}
              >
                {item.label}
              </span>
            </div>
          ))}
        </div>
      </nav>

      <div className="rotate-[-0.11deg] [font-family:'Inter',Helvetica] font-medium text-gray-500 text-sm tracking-[0] leading-[21px] whitespace-nowrap">
        Version 1.2
      </div>
    </header>
  );
};
