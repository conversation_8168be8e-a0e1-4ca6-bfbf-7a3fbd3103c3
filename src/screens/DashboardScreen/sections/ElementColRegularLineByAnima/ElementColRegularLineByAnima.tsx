import {
  CheckIcon,
  MoreVerticalIcon,
  SearchIcon,
  SlidersHorizontalIcon,
} from "lucide-react";
import React from "react";
import { Badge } from "../../../../components/ui/badge";
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Input } from "../../../../components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../../components/ui/table";

export const ElementColRegularLineByAnima = (): JSX.Element => {
  // Table data for rules
  const rules = [
    {
      id: 1,
      name: "Holiday Sale",
      type: "Percentage",
      conditions: "Min cart: $50, Category: Electronics",
      discountValue: "20%",
      status: "Active",
      activePeriod: "01/12/24 - 16/12/24",
      isSelected: false,
    },
    {
      id: 2,
      name: "Holiday Sale",
      type: "Fixed Amount",
      conditions: "Min cart: $50, Category: Electronics",
      discountValue: "20%",
      status: "Active",
      activePeriod: "01/12/24 - 16/12/24",
      isSelected: true,
    },
    {
      id: 3,
      name: "Holiday Sale",
      type: "BOGO",
      conditions: "Min cart: $50, Category: Electronics",
      discountValue: "20%",
      status: "Expired",
      activePeriod: "01/12/24 - 16/12/24",
      isSelected: false,
    },
    {
      id: 4,
      name: "Holiday Sale",
      type: "Tiered",
      conditions: "Min cart: $50, Category: Electronics",
      discountValue: "20%",
      status: "Active",
      activePeriod: "01/12/24 - 16/12/24",
      isSelected: false,
    },
    {
      id: 5,
      name: "Holiday Sale",
      type: "Percentage",
      conditions: "Min cart: $50, Category: Electronics",
      discountValue: "20%",
      status: "Expired",
      activePeriod: "01/12/24 - 16/12/24",
      isSelected: false,
    },
  ];

  return (
    <Card className="flex flex-col w-full items-start gap-5 p-5 bg-neutralwhitebase-color rounded-lg">
      <CardContent className="flex flex-col w-full p-0 gap-5">
        <div className="flex items-start justify-between relative self-stretch w-full">
          <div className="flex items-center gap-4">
            <h2 className="font-semibold text-gray-700 text-xl tracking-[0.10px] leading-[30px] whitespace-nowrap">
              Active Rules
            </h2>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 relative">
              <div className="relative flex items-center w-[476px]">
                <SearchIcon className="absolute left-2 w-[18px] h-[18px] text-gray-500" />
                <Input
                  className="pl-8 pr-3 py-2 text-sm border border-solid border-[#e0e2e7] rounded-lg"
                  placeholder="Search"
                />
              </div>
            </div>

            <Button
              variant="outline"
              className="flex items-center gap-2 border border-solid border-[#e0e2e7] rounded-lg"
            >
              <SlidersHorizontalIcon className="w-5 h-5" />
              <span className="font-text-m-medium text-neutralgraygray-500">
                Filters
              </span>
            </Button>
          </div>
        </div>

        <Table>
          <TableHeader className="bg-[#f8f9fb] rounded">
            <TableRow className="h-10">
              <TableHead className="w-[50px] p-0">
                <div className="flex items-center justify-center h-full px-4 py-1.5">
                  <Checkbox className="w-5 h-5 rounded bg-[#fcfcfc] shadow-[0px_0px_0px_1.5px_#edeff5,0px_0px_2px_1px_#dfe2ed80]" />
                </div>
              </TableHead>
              <TableHead className="w-[140px]">
                <div className="font-paragraph-p2-medium text-[#222b3d]">
                  Name
                </div>
              </TableHead>
              <TableHead className="w-[164px]">
                <div className="font-paragraph-p2-medium text-[#222b3d]">
                  Type
                </div>
              </TableHead>
              <TableHead className="w-[239px]">
                <div className="font-paragraph-p2-medium text-[#222b3d]">
                  Conditions
                </div>
              </TableHead>
              <TableHead className="w-[150px]">
                <div className="font-paragraph-p2-medium text-[#222b3d]">
                  Discount Value
                </div>
              </TableHead>
              <TableHead className="w-[140px]">
                <div className="flex items-center gap-3">
                  <span className="font-paragraph-p2-medium text-[#222b3d]">
                    Status
                  </span>
                  <img className="w-4 h-4" alt="Icons" src="/icons-10.svg" />
                </div>
              </TableHead>
              <TableHead className="w-[190px]">
                <div className="font-paragraph-p2-medium text-[#222b3d]">
                  Active Period
                </div>
              </TableHead>
              <TableHead className="w-12 p-0"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rules.map((rule) => (
              <TableRow
                key={rule.id}
                className="h-12 border-b border-[#dfe2ed]"
              >
                <TableCell className="p-0">
                  <div className="flex items-center justify-center h-full px-4 py-1.5">
                    {rule.isSelected ? (
                      <div className="w-5 h-5 bg-blue-1 rounded overflow-hidden flex items-center justify-center">
                        <CheckIcon className="w-5 h-5 text-white" />
                      </div>
                    ) : (
                      <Checkbox className="w-5 h-5 rounded bg-[#fcfcfc] shadow-[0px_0px_0px_1.5px_#edeff5,0px_0px_2px_1px_#dfe2ed80]" />
                    )}
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <span className="font-paragraph-p2 text-[#6a748c]">
                      {rule.name}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <span className="font-paragraph-p2 text-[#6a748c]">
                      {rule.type}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <span className="font-paragraph-p2 text-[#6a748c]">
                      {rule.conditions}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <span className="font-paragraph-p2 text-[#6a748c]">
                      {rule.discountValue}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <Badge
                      className={`px-2 py-1 rounded-[20px] ${
                        rule.status === "Active"
                          ? "bg-[#d8fae7] text-[#357a5f]"
                          : "bg-[#ffd5c0] text-[#af4611]"
                      }`}
                    >
                      <span className="font-label-text-md">
                        {rule.status === "Active" ? "Actice" : "Expired"}
                      </span>
                    </Badge>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center h-full px-4 py-1.5">
                    <span className="font-paragraph-p2 text-[#6a748c]">
                      {rule.activePeriod}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="p-0">
                  <div className="flex items-center justify-center h-full px-4 py-1.5">
                    <MoreVerticalIcon className="w-5 h-5 text-gray-500" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
