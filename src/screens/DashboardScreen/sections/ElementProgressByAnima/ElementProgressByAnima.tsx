import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../../components/ui/card";

export const ElementProgressByAnima = (): JSX.Element => {
  return (
    <Card className="w-full max-w-[570px] h-auto">
      <CardHeader className="pb-0">
        <CardTitle className="font-semibold text-gray-700 text-xl tracking-[0.10px] leading-[30px]">
          Discount Alert vs No Discount Alert
        </CardTitle>
        <CardDescription className="font-medium text-gray-500 text-sm tracking-[0.07px] leading-5">
          Revenue Target
        </CardDescription>
      </CardHeader>

      <CardContent className="flex flex-col items-center gap-8 pt-5">
        <div className="relative w-full max-w-[344px] h-[172px]">
          <div className="relative w-full max-w-[414px] h-[187px] -top-2 left-1/2 -translate-x-1/2">
            <img
              className="absolute w-[359px] h-[187px] top-0 left-1/2 -translate-x-1/2"
              alt="Track"
              src="/track.svg"
            />

            <img
              className="absolute w-[308px] h-[187px] top-0 left-1/2 -translate-x-1/2"
              alt="Progress"
              src="/progress.svg"
            />

            <div className="flex flex-col items-center justify-center gap-[5.11px] absolute top-[75px] left-0 w-full">
              <div className="font-semibold text-gray-700 text-[35.8px] tracking-[0.18px] leading-[normal]">
                75.55%
              </div>

              <div className="w-full font-normal text-gray-600 text-base text-center tracking-[0] leading-5">
                Sales Come From Discount Alert
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col items-center gap-2.5 w-full">
          <p className="w-full text-center font-normal text-gray-600 text-[17.9px] leading-[17.9px]">
            <span className="text-[#475466] tracking-[0] leading-[0.1px]">
              You succeed earn{" "}
            </span>
            <span className="font-semibold text-[#344053] tracking-[0.02px] leading-[25.6px]">
              $240
            </span>
            <span className="text-[#344053] tracking-[0.02px] leading-[25.6px]">
              {" "}
              by using Discount alert
            </span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
