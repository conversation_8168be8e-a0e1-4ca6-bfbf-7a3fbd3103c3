import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const ElementColRegularLineWrapperByAnima = (): JSX.Element => {
  // Y-axis data points
  const yAxisValues = ["1.2k", "1k", "800", "600", "400", "200", "0"];

  // Months data for x-axis
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  return (
    <Card className="w-full max-w-[571px] bg-neutralwhitebase-color rounded-lg">
      <CardContent className="flex flex-col items-start gap-5 p-5">
        <div className="flex items-start gap-3 relative self-stretch w-full flex-[0_0_auto] z-[1]">
          <div className="flex flex-col items-start gap-0.5 relative flex-1 grow">
            <h3 className="relative self-stretch mt-[-1.00px] font-['Manrope',Helvetica] font-semibold text-gray-700 text-xl tracking-[0.10px] leading-[30px]">
              Clicks
            </h3>
            <p className="relative w-fit font-['Manrope',Helvetica] font-medium text-gray-600 text-sm tracking-[0.07px] leading-5 whitespace-nowrap">
              Sales through Discount alert
            </p>
          </div>
        </div>

        <div className="flex flex-col items-end pt-0 pb-[18px] px-0 relative self-stretch w-full flex-[0_0_auto] z-0">
          <div className="flex-col items-start gap-[18px] flex relative self-stretch w-full flex-[0_0_auto]">
            {yAxisValues.map((value, index) => (
              <div
                key={index}
                className="items-center gap-2 flex relative self-stretch w-full flex-[0_0_auto]"
              >
                <div className="relative w-10 mt-[-1.00px] font-['Public_Sans',Helvetica] font-normal text-neutralgraygray-500 text-xs text-right tracking-[0.06px] leading-[18px]">
                  {value}
                </div>
                <img
                  className="relative flex-1 grow h-px object-cover"
                  alt="Line"
                  src="/line.svg"
                />
              </div>
            ))}
          </div>

          <img
            className="absolute w-[483px] h-[216px] top-[9px] left-12"
            alt="Line"
            src="/line-1.svg"
          />

          <div className="flex w-[483px] h-[243px] items-end gap-2 absolute top-[9px] left-12">
            {months.map((month, index) => (
              <div
                key={index}
                className={`flex flex-col items-center justify-end ${
                  month === "Jul" ? "gap-[9px]" : "gap-4"
                } relative flex-1 self-stretch grow`}
              >
                {month === "Jul" ? (
                  <>
                    <div className="inline-flex flex-col h-[199px] items-center gap-1 relative ml-[-43.54px] mr-[-43.54px]">
                      <div className="inline-flex flex-col items-start gap-1.5 px-4 py-3 relative flex-[0_0_auto] bg-blackblack-500 rounded-lg overflow-hidden shadow-soft-shadow">
                        <div className="inline-flex items-center gap-1.5 relative flex-[0_0_auto]">
                          <div className="relative w-2.5 h-2.5 rounded-[5px] [background:linear-gradient(180deg,rgba(255,111,78,1)_0%,rgba(255,87,78,1)_100%)]" />
                          <div className="relative w-fit mt-[-1.00px] font-['Manrope',Helvetica] font-normal text-neutralblackblack-100 text-xs tracking-[0.06px] leading-[18px] whitespace-nowrap">
                            Clicks
                          </div>
                          <div className="relative w-fit mt-[-1.00px] font-text-s-regular font-[number:var(--text-s-regular-font-weight)] text-white text-[length:var(--text-s-regular-font-size)] tracking-[var(--text-s-regular-letter-spacing)] leading-[var(--text-s-regular-line-height)] whitespace-nowrap [font-style:var(--text-s-regular-font-style)]">
                            :
                          </div>
                          <div className="relative w-fit mt-[-1.00px] font-['Manrope',Helvetica] font-semibold text-white text-xs tracking-[0.06px] leading-[18px] whitespace-nowrap">
                            480
                          </div>
                        </div>
                      </div>
                      <div className="relative w-3 h-3 rounded-md border-2 border-solid border-white [background:linear-gradient(180deg,rgba(255,119,78,1)_0%,rgba(255,78,78,1)_100%)]" />
                      <img
                        className="relative flex-1 w-px grow"
                        alt="Line"
                        src="/line-2.svg"
                      />
                    </div>
                    <div className="relative w-fit font-text-s-bold font-[number:var(--text-s-bold-font-weight)] text-[#ff6132] text-[length:var(--text-s-bold-font-size)] text-center tracking-[var(--text-s-bold-letter-spacing)] leading-[var(--text-s-bold-line-height)] whitespace-nowrap [font-style:var(--text-s-bold-font-style)]">
                      Jul
                    </div>
                  </>
                ) : (
                  <div className="relative w-fit font-['Public_Sans',Helvetica] font-normal text-neutralgraygray-500 text-xs text-center tracking-[0.06px] leading-[18px] whitespace-nowrap">
                    {month}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
