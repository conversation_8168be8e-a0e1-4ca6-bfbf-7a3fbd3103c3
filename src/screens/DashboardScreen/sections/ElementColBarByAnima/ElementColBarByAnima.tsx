import { PlusCircleIcon } from "lucide-react";
import React from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";

export const ElementColBarByAnima = (): JSX.Element => {
  // Data for the chart grid lines
  const gridLines = [
    { value: "$1.2k" },
    { value: "$1k" },
    { value: "$800" },
    { value: "$600" },
    { value: "$400" },
    { value: "$200" },
    { value: "0" },
  ];

  // Data for the bars
  const barData = [
    {
      name: "Holiday Sale",
      height: "111px",
      top: "70px",
      left: "0",
      highlighted: false,
    },
    {
      name: "VIP Discount",
      height: "111px",
      top: "70px",
      left: "102px",
      highlighted: false,
    },
    {
      name: "Weekend Promo",
      height: "176px",
      top: "5px",
      left: "204px",
      highlighted: false,
    },
    {
      name: "Black Friday",
      height: "94px",
      top: "87px",
      left: "330px",
      highlighted: true,
    },
    {
      name: "Holiday Sale",
      height: "142px",
      top: "39px",
      left: "441px",
      highlighted: false,
    },
    {
      name: "Year End",
      height: "181px",
      top: "0",
      left: "565px",
      highlighted: false,
    },
    {
      name: "VIP Discount",
      height: "111px",
      top: "70px",
      left: "666px",
      highlighted: false,
    },
  ];

  return (
    <Card className="w-full max-w-[865px] bg-neutralwhite rounded-lg">
      <CardContent className="flex flex-col items-start gap-6 p-6">
        <div className="flex items-start gap-3 relative self-stretch w-full flex-[0_0_auto] z-[1]">
          <div className="flex flex-col items-start gap-0.5 relative flex-1 grow">
            <h2 className="relative self-stretch mt-[-1.00px] font-['Manrope',Helvetica] font-semibold text-gray-700 text-xl tracking-[0.10px] leading-[30px]">
              Best Performing
            </h2>
            <p className="relative self-stretch font-['Manrope',Helvetica] font-medium text-gray-600 text-sm tracking-[0.07px] leading-5">
              Alerts generate most revenue
            </p>
          </div>

          <Button className="h-9 shadow-[inset_0px_-4px_9px_#ffb499] bg-[#ff6f44] rounded-[28px] text-white font-semibold text-sm">
            <PlusCircleIcon className="w-[18px] h-[18px] mr-2" />
            Add new rules
          </Button>
        </div>

        <div className="flex flex-col items-end pt-0 pb-[18px] px-0 relative self-stretch w-full flex-[0_0_auto] z-0">
          <div className="flex-col items-start gap-[18px] flex relative self-stretch w-full flex-[0_0_auto]">
            {gridLines.map((line, index) => (
              <div
                key={index}
                className="flex items-center gap-2 relative self-stretch w-full flex-[0_0_auto]"
              >
                <div className="relative w-10 mt-[-1.00px] font-['Public_Sans',Helvetica] font-normal text-neutralgraygray-500 text-xs text-right tracking-[0.06px] leading-[18px]">
                  {line.value}
                </div>
                <img
                  className="relative flex-1 grow h-px object-cover"
                  alt="Line"
                  src="/line.svg"
                />
              </div>
            ))}

            <div className="absolute w-[714px] h-[181px] top-[45px] left-[87px]">
              {barData.map((bar, index) => (
                <div
                  key={index}
                  className={`h-[${bar.height}] top-[${bar.top}] left-[${bar.left}] ${
                    bar.highlighted ? "bg-[#ff774e]" : "bg-[#fff2ef]"
                  } absolute w-[42px] rounded-lg`}
                />
              ))}
            </div>

            <div className="inline-flex flex-col items-start gap-1.5 px-4 py-3 absolute top-[84px] left-[391px] bg-blackblack-500 rounded-lg overflow-hidden shadow-soft-shadow">
              <div className="inline-flex items-center gap-1.5 relative flex-[0_0_auto]">
                <div className="relative w-fit mt-[-1.00px] font-['Manrope',Helvetica] font-normal text-neutralblackblack-100 text-xs tracking-[0.06px] leading-[18px] whitespace-nowrap">
                  Sales
                </div>
                <div className="relative w-fit mt-[-1.00px] font-text-s-regular font-[number:var(--text-s-regular-font-weight)] text-white text-[length:var(--text-s-regular-font-size)] tracking-[var(--text-s-regular-letter-spacing)] leading-[var(--text-s-regular-line-height)] whitespace-nowrap [font-style:var(--text-s-regular-font-style)]">
                  :
                </div>
                <div className="relative w-fit mt-[-1.00px] font-['Manrope',Helvetica] font-semibold text-white text-xs tracking-[0.06px] leading-[18px] whitespace-nowrap">
                  $510
                </div>
              </div>
            </div>
          </div>

          <div className="absolute w-[738px] h-[18px] top-[234px] left-[79px]">
            {barData.map((bar, index) => (
              <div
                key={index}
                className={`absolute h-[18px] -top-px left-[${bar.left}] font-['Manrope',Helvetica] ${
                  bar.highlighted
                    ? "font-semibold text-[#ff774e]"
                    : "font-medium text-neutralgraygray-500"
                } text-xs text-center tracking-[0.06px] leading-[18px] whitespace-nowrap`}
              >
                {bar.name}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
