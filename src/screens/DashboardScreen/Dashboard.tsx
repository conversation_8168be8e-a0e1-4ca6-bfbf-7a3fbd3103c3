import { Badge } from "../../components/ui/badge";
import { Card, CardContent } from "../../components/ui/card";
import { ElementColBarByAnima } from "./sections/ElementColBarByAnima";
import { ElementColRegularLineByAnima } from "./sections/ElementColRegularLineByAnima/ElementColRegularLineByAnima";
import { ElementColRegularLineWrapperByAnima } from "./sections/ElementColRegularLineWrapperByAnima/ElementColRegularLineWrapperByAnima";
import { ElementProgressByAnima } from "./sections/ElementProgressByAnima";
import { FrameByAnima } from "./sections/FrameByAnima";

export const Dashboard = (): JSX.Element => {
    // Data for stat cards
    const statCards = [
        {
            title: "Active Discounts",
            value: "124",
            left: "140px",
        },
        {
            title: "Upcoming Discounts",
            value: "13",
            left: "435px",
        },
        {
            title: "Expired Discounts",
            value: "324",
            left: "730px",
        },
    ];

    // Data for notifications
    const notifications = [
        {
            title: "Holiday Sale Ends Tomorrow!",
            description: (
                <>
                    <span className="text-[#475466]">
                        Your &#39;Holiday Sale&#39; is set to expire on{" "}
                    </span>
                    <span className="font-semibold text-[#344053]">
                        January 7, 2025
                    </span>
                </>
            ),
            date: "Jan 03",
            tags: ["Proposal", "Preparation"],
            tagColors: ["#ffd5c0", "#d7e4ff"],
            tagTextColors: ["#af4611", "#122368"],
        },
        {
            title: "Weekend Promo Drives 200 Sales!",
            description:
                "Your 'Weekend Promo' generated 200 sales and $10,000 in revenue.",
            date: "Jan 03",
            tags: ["Proposal", "Preparation"],
            tagColors: ["#ffd5c0", "#d7e4ff"],
            tagTextColors: ["#af4611", "#122368"],
        },
    ];

    return (
        <div className="relative w-full min-h-screen bg-[#f0f3f9]">
            <FrameByAnima />

            {/* Stat Cards */}
            {statCards.map((card, index) => (
                <Card
                    key={index}
                    className={`flex flex-col w-[275px] items-start gap-2.5 px-4 py-5 absolute top-[124px] left-[${card.left}] bg-white rounded-2xl overflow-hidden`}
                >
                    <CardContent className="flex items-center gap-4 relative self-stretch w-full p-0">
                        <div className="relative w-16 h-16">
                            <div className="flex w-16 h-16 items-center justify-center gap-[11.43px] px-[13.71px] py-[12.57px] relative bg-[#fef6f4] rounded-[32px]">
                                <img
                                    className="relative w-10 h-10 mt-[-0.57px] mb-[-0.57px] ml-[-1.71px] mr-[-1.71px]"
                                    alt="Icon"
                                    src="/icon.svg"
                                />
                            </div>
                        </div>

                        <div className="relative w-36 h-[59px]">
                            <div className="flex flex-col w-36 items-start gap-1 relative">
                                <div className="relative self-stretch mt-[-1.00px] [font-family:'Manrope',Helvetica] font-medium text-gray-500 text-sm tracking-[-0.28px] leading-6">
                                    {card.title}
                                </div>

                                <div className="relative self-stretch h-[31px] [font-family:'Manrope',Helvetica] font-bold text-gray-700 text-2xl tracking-[-0.48px] leading-8 whitespace-nowrap">
                                    {card.value}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            ))}

            <ElementColBarByAnima />

            {/* Upgrade to PRO Card */}
            <Card className="flex flex-col w-[275px] items-start gap-2.5 p-8 absolute top-[124px] left-[1025px] rounded-xl overflow-hidden border border-solid border-white shadow-[0px_0px_0px_3px_#ff9c7d21] [background:linear-gradient(0deg,rgba(255,255,255,1)_0%,rgba(255,226,219,1)_100%)]">
                <CardContent className="flex flex-col items-center gap-6 relative self-stretch w-full p-0">
                    <div className="inline-flex items-center gap-2.5 p-3 relative flex-[0_0_auto] rounded-[100px] overflow-hidden shadow-[inset_0px_-1px_7px_2px_#ffffff] [background:linear-gradient(180deg,rgba(255,224,215,1)_0%,rgba(255,255,255,1)_100%)]">
                        <div className="relative w-8 h-8">
                            <div className="relative w-[30px] h-[23px] top-1 left-px bg-[url(/group.png)] bg-[100%_100%]" />
                        </div>
                    </div>

                    <div className="flex flex-col items-center gap-2 relative self-stretch w-full flex-[0_0_auto]">
                        <div className="relative self-stretch mt-[-1.00px] [font-family:'Manrope',Helvetica] font-bold text-gray-800 text-xl text-center tracking-[0] leading-[normal]">
                            Upgrade to PRO
                        </div>

                        <div className="relative self-stretch [font-family:'Manrope',Helvetica] font-normal text-gray-500 text-xs text-center tracking-[0] leading-[normal]">
                            to get access to all features! and many more
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Notice Card */}
            <Card className="inline-flex h-[269px] items-center gap-2.5 p-3.5 absolute top-[355px] left-[1025px] bg-white rounded-2xl overflow-hidden">
                <CardContent className="inline-flex flex-col h-[242px] items-start gap-3 relative flex-[0_0_auto] mt-[-0.50px] mb-[-0.50px] p-0">
                    <div className="relative w-fit mt-[-1.00px] [font-family:'Manrope',Helvetica] font-semibold text-gray-700 text-base tracking-[0.08px] leading-[normal]">
                        Notice
                    </div>

                    {notifications.map((notification, index) => (
                        <div
                            key={index}
                            className={`flex flex-col w-[243px] items-start gap-3 pt-0 pb-3 px-0 relative flex-[0_0_auto] ${
                                index < notifications.length - 1
                                    ? "border-b [border-bottom-style:solid] border-[#dfe2ed]"
                                    : ""
                            }`}
                        >
                            <div className="flex flex-col items-start gap-1 relative self-stretch w-full flex-[0_0_auto]">
                                <div className="relative self-stretch mt-[-1.00px] [font-family:'Manrope',Helvetica] font-medium text-gray-700 text-sm tracking-[0] leading-[22.4px]">
                                    {notification.title}
                                </div>

                                <div className="relative self-stretch [font-family:'Manrope',Helvetica] font-normal text-gray-600 text-xs tracking-[0] leading-[normal]">
                                    {notification.description}
                                </div>
                            </div>

                            <div className="flex w-[243px] items-center justify-between relative flex-[0_0_auto]">
                                <div className="inline-flex items-start gap-1 relative flex-[0_0_auto]">
                                    {notification.tags.map((tag, tagIndex) => (
                                        <Badge
                                            key={tagIndex}
                                            className={`inline-flex items-center justify-center gap-2 px-1.5 py-0.5 relative flex-[0_0_auto] bg-[${notification.tagColors[tagIndex]}] rounded-[29px] overflow-hidden`}
                                        >
                                            <div
                                                className={`relative w-fit mt-[-1.00px] [font-family:'Switzer-Regular',Helvetica] font-normal text-[${notification.tagTextColors[tagIndex]}] text-xs tracking-[0] leading-[normal]`}
                                            >
                                                {tag}
                                            </div>
                                        </Badge>
                                    ))}
                                </div>

                                <div className="inline-flex items-center gap-1 relative flex-[0_0_auto]">
                                    <div className="relative w-fit mt-[-1.00px] [font-family:'Switzer-Regular',Helvetica] font-normal text-gray-600 text-xs tracking-[0] leading-[normal]">
                                        {notification.date}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </CardContent>
            </Card>

            <ElementColRegularLineByAnima />
            <ElementColRegularLineWrapperByAnima />
            <ElementProgressByAnima />
        </div>
    );
};
