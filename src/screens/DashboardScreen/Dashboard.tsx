import { Badge } from "../../components/ui/badge";
import { Card, CardContent } from "../../components/ui/card";
import { ElementColBarByAnima } from "./sections/ElementColBarByAnima";
import { ElementColRegularLineByAnima } from "./sections/ElementColRegularLineByAnima/ElementColRegularLineByAnima";
import { ElementColRegularLineWrapperByAnima } from "./sections/ElementColRegularLineWrapperByAnima/ElementColRegularLineWrapperByAnima";
import { ElementProgressByAnima } from "./sections/ElementProgressByAnima";
import { FrameByAnima } from "./sections/FrameByAnima";

export const Dashboard = (): JSX.Element => {
    // Data for stat cards
    const statCards = [
        {
            title: "Active Discounts",
            value: "124",
        },
        {
            title: "Upcoming Discounts",
            value: "13",
        },
        {
            title: "Expired Discounts",
            value: "324",
        },
    ];

    // Data for notifications
    const notifications = [
        {
            title: "Holiday Sale Ends Tomorrow!",
            description: (
                <>
                    <span className="text-[#475466]">
                        Your &#39;Holiday Sale&#39; is set to expire on{" "}
                    </span>
                    <span className="font-semibold text-[#344053]">
                        January 7, 2025
                    </span>
                </>
            ),
            date: "Jan 03",
            tags: ["Proposal", "Preparation"],
            tagColors: ["#ffd5c0", "#d7e4ff"],
            tagTextColors: ["#af4611", "#122368"],
        },
        {
            title: "Weekend Promo Drives 200 Sales!",
            description:
                "Your 'Weekend Promo' generated 200 sales and $10,000 in revenue.",
            date: "Jan 03",
            tags: ["Proposal", "Preparation"],
            tagColors: ["#ffd5c0", "#d7e4ff"],
            tagTextColors: ["#af4611", "#122368"],
        },
    ];

    return (
        <div className="w-full min-h-screen bg-[#f0f3f9]">
            <FrameByAnima />

            <div className="px-[140px] py-8 space-y-8">
                {/* Stat Cards Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {statCards.map((card, index) => (
                        <Card
                            key={index}
                            className="flex flex-col items-start gap-2.5 px-4 py-5 bg-white rounded-2xl"
                        >
                            <CardContent className="flex items-center gap-4 w-full p-0">
                                <div className="w-16 h-16">
                                    <div className="flex w-16 h-16 items-center justify-center bg-[#fef6f4] rounded-[32px]">
                                        <img
                                            className="w-10 h-10"
                                            alt="Icon"
                                            src="/icon.svg"
                                        />
                                    </div>
                                </div>

                                <div className="flex-1">
                                    <div className="flex flex-col gap-1">
                                        <div className="font-['Manrope',Helvetica] font-medium text-gray-500 text-sm tracking-[-0.28px] leading-6">
                                            {card.title}
                                        </div>
                                        <div className="font-['Manrope',Helvetica] font-bold text-gray-700 text-2xl tracking-[-0.48px] leading-8">
                                            {card.value}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}

                    {/* Upgrade to PRO Card */}
                    <Card className="flex flex-col items-start gap-2.5 p-8 rounded-xl border border-white shadow-[0px_0px_0px_3px_#ff9c7d21] bg-gradient-to-t from-[#ffe2db] to-white">
                        <CardContent className="flex flex-col items-center gap-6 w-full p-0">
                            <div className="inline-flex items-center gap-2.5 p-3 rounded-full shadow-[inset_0px_-1px_7px_2px_#ffffff] bg-gradient-to-b from-[#ffe0d7] to-white">
                                <div className="w-8 h-8">
                                    <div className="w-[30px] h-[23px] mt-1 ml-px bg-[url(/group.png)] bg-cover" />
                                </div>
                            </div>

                            <div className="flex flex-col items-center gap-2 w-full">
                                <div className="font-['Manrope',Helvetica] font-bold text-gray-800 text-xl text-center">
                                    Upgrade to PRO
                                </div>
                                <div className="font-['Manrope',Helvetica] font-normal text-gray-500 text-xs text-center">
                                    to get access to all features! and many more
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Chart Section - Takes 3 columns */}
                    <div className="lg:col-span-3">
                        <ElementColBarByAnima />
                    </div>

                    {/* Notice Card - Takes 1 column */}
                    <div className="lg:col-span-1">
                        <Card className="h-[269px] p-3.5 bg-white rounded-2xl">
                            <CardContent className="flex flex-col h-full gap-3 p-0">
                                <div className="font-['Manrope',Helvetica] font-semibold text-gray-700 text-base tracking-[0.08px]">
                                    Notice
                                </div>

                                <div className="flex-1 space-y-3">
                                    {notifications.map(
                                        (notification, index) => (
                                            <div
                                                key={index}
                                                className={`flex flex-col gap-3 pb-3 ${
                                                    index <
                                                    notifications.length - 1
                                                        ? "border-b border-[#dfe2ed]"
                                                        : ""
                                                }`}
                                            >
                                                <div className="flex flex-col gap-1">
                                                    <div className="font-['Manrope',Helvetica] font-medium text-gray-700 text-sm leading-[22.4px]">
                                                        {notification.title}
                                                    </div>
                                                    <div className="font-['Manrope',Helvetica] font-normal text-gray-600 text-xs">
                                                        {
                                                            notification.description
                                                        }
                                                    </div>
                                                </div>

                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-start gap-1">
                                                        {notification.tags.map(
                                                            (tag, tagIndex) => (
                                                                <Badge
                                                                    key={
                                                                        tagIndex
                                                                    }
                                                                    className="px-1.5 py-0.5 rounded-[29px]"
                                                                    style={{
                                                                        backgroundColor:
                                                                            notification
                                                                                .tagColors[
                                                                                tagIndex
                                                                            ],
                                                                        color: notification
                                                                            .tagTextColors[
                                                                            tagIndex
                                                                        ],
                                                                    }}
                                                                >
                                                                    <div className="font-['Switzer-Regular',Helvetica] font-normal text-xs">
                                                                        {tag}
                                                                    </div>
                                                                </Badge>
                                                            )
                                                        )}
                                                    </div>

                                                    <div className="font-['Switzer-Regular',Helvetica] font-normal text-gray-600 text-xs">
                                                        {notification.date}
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <ElementColRegularLineByAnima />
                    <ElementColRegularLineWrapperByAnima />
                </div>

                <ElementProgressByAnima />
            </div>
        </div>
    );
};
