{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "anima-project", "description": "A React project automatically generated by <PERSON><PERSON> using the Shadcn UI library", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.0.4", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "date-fns": "^3.3.1", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "2.5.4"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "^10.4.17", "esbuild": "0.24.0", "globals": "15.12.0", "postcss": "^8.4.35", "tailwindcss": "3.4.16", "typescript": "^5.3.3", "vite": "6.0.4"}}