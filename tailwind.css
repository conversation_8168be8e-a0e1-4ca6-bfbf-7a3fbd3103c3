@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    .all-\[unset\] {
        all: unset;
    }
}

:root {
    --blackblack-400: rgba(74, 76, 86, 1);
    --blackblack-500: rgba(29, 31, 44, 1);
    --blue-1: rgba(47, 128, 237, 1);
    --button-text-md-font-family: "Inter", Helvetica;
    --button-text-md-font-size: 16px;
    --button-text-md-font-style: normal;
    --button-text-md-font-weight: 500;
    --button-text-md-letter-spacing: 0px;
    --button-text-md-line-height: 120.00000762939453%;
    --gray-400: rgba(152, 162, 179, 1);
    --gray-500: rgba(102, 112, 133, 1);
    --gray-600: rgba(71, 84, 103, 1);
    --gray-700: rgba(52, 64, 84, 1);
    --gray-800: rgba(29, 41, 57, 1);
    --label-text-md-font-family: "Inter", Helvetica;
    --label-text-md-font-size: 14px;
    --label-text-md-font-style: normal;
    --label-text-md-font-weight: 500;
    --label-text-md-letter-spacing: 0px;
    --label-text-md-line-height: 120.00000762939453%;
    --neutralblackblack-100: rgba(210, 210, 213, 1);
    --neutralgraygray-100: rgba(224, 226, 231, 1);
    --neutralgraygray-200: rgba(194, 198, 206, 1);
    --neutralgraygray-50: rgba(240, 241, 243, 1);
    --neutralgraygray-500: rgba(102, 112, 133, 1);
    --neutralwhite: rgba(255, 255, 255, 1);
    --neutralwhitebase-color: rgba(255, 255, 255, 1);
    --paragraph-p2-font-family: "Inter", Helvetica;
    --paragraph-p2-font-size: 14px;
    --paragraph-p2-font-style: normal;
    --paragraph-p2-font-weight: 400;
    --paragraph-p2-letter-spacing: 0px;
    --paragraph-p2-line-height: 150%;
    --paragraph-p2-medium-font-family: "Inter", Helvetica;
    --paragraph-p2-medium-font-size: 14px;
    --paragraph-p2-medium-font-style: normal;
    --paragraph-p2-medium-font-weight: 500;
    --paragraph-p2-medium-letter-spacing: 0px;
    --paragraph-p2-medium-line-height: 150%;
    --soft-shadow: 0px 4px 30px 0px rgba(46, 45, 116, 0.05);
    --text-m-medium-font-family: "Public Sans", Helvetica;
    --text-m-medium-font-size: 14px;
    --text-m-medium-font-style: normal;
    --text-m-medium-font-weight: 500;
    --text-m-medium-letter-spacing: 0.07000000000000002px;
    --text-m-medium-line-height: 20px;
    --text-s-bold-font-family: "Public Sans", Helvetica;
    --text-s-bold-font-size: 12px;
    --text-s-bold-font-style: normal;
    --text-s-bold-font-weight: 700;
    --text-s-bold-letter-spacing: 0.06px;
    --text-s-bold-line-height: 18px;
    --text-s-regular-font-family: "Public Sans", Helvetica;
    --text-s-regular-font-size: 12px;
    --text-s-regular-font-style: normal;
    --text-s-regular-font-weight: 400;
    --text-s-regular-letter-spacing: 0.06px;
    --text-s-regular-line-height: 18px;
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 47.4% 11.2%;

        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 222.2 47.4% 11.2%;

        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;

        --card: transparent;
        --card-foreground: 222.2 47.4% 11.2%;

        --primary: 222.2 47.4% 11.2%;
        --primary-foreground: 210 40% 98%;

        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;

        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;

        --destructive: 0 100% 50%;
        --destructive-foreground: 210 40% 98%;

        --ring: 215 20.2% 65.1%;

        --radius: 0.5rem;
    }

    .dark {
        --background: 224 71% 4%;
        --foreground: 213 31% 91%;

        --muted: 223 47% 11%;
        --muted-foreground: 215.4 16.3% 56.9%;

        --accent: 216 34% 17%;
        --accent-foreground: 210 40% 98%;

        --popover: 224 71% 4%;
        --popover-foreground: 215 20.2% 65.1%;

        --border: 216 34% 17%;
        --input: 216 34% 17%;

        --card: transparent;
        --card-foreground: 213 31% 91%;

        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 1.2%;

        --secondary: 222.2 47.4% 11.2%;
        --secondary-foreground: 210 40% 98%;

        --destructive: 0 63% 31%;
        --destructive-foreground: 210 40% 98%;

        --ring: 216 34% 17%;

        --radius: 0.5rem;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
        font-feature-settings: "rlig" 1, "calt" 1;
    }
}